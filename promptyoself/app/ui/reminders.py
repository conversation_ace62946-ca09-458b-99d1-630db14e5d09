# -*- coding: utf-8 -*-
"""Reminder UI views."""
from flask import Blueprint, render_template, request, flash, redirect, url_for, abort

from ..forms import ReminderForm
from ..models import <PERSON>minder, Task, Project  # Added Project
from ..extensions import db
from ..utils import flash_errors

blueprint = Blueprint("reminders", __name__, url_prefix="/reminders")


@blueprint.route("/")
def list_reminders():  # Renamed for clarity
    """List all reminders with pagination and filtering."""
    page = request.args.get("page", 1, type=int)
    # Default to 10 items per page
    per_page = request.args.get("per_page", 10, type=int)
    task_id_filter = request.args.get("task_id", None, type=int)
    process_name_filter = request.args.get("process_name", None, type=str)

    query = Reminder.query.join(Task).join(Project)
    if task_id_filter:
        query = query.filter(Reminder.task_id == task_id_filter)  # type: ignore
    if process_name_filter:
        query = query.filter(Reminder.process_name.ilike(
            f"%{process_name_filter}%"))

    paginated_reminders = query.order_by(Project.name, Task.name, Reminder.next_run).paginate(  # type: ignore
        page=page, per_page=per_page, error_out=False)

    tasks = Task.query.join(Project).order_by(
        Project.name, Task.name).all()  # For filter dropdown

    return render_template("reminders/list.html", paginated_reminders=paginated_reminders, task_id_filter=task_id_filter, process_name_filter=process_name_filter, tasks=tasks)


@blueprint.route("/<int:reminder_id>")
def view_reminder(reminder_id):
    """View a specific reminder with its details."""
    reminder = Reminder.query.get_or_404(reminder_id)
    return render_template("reminders/detail.html", reminder=reminder)


@blueprint.route("/new", methods=["GET", "POST"])
def new_reminder():  # Renamed for clarity
    """Create a new reminder."""
    form = ReminderForm(request.form)
    if form.validate_on_submit():
        reminder = Reminder.create(
            message=form.message.data,
            next_run=form.next_run.data,
            recurrence=form.recurrence.data or None,
            process_name=form.process_name.data,
            task_id=form.task_id.data
        )
        flash(
            f"Reminder for task '{reminder.task.name}' created successfully.", "success")
        return redirect(url_for("reminders.list_reminders"))
    else:
        flash_errors(form)
    return render_template("reminders/form.html", form=form, title="New Reminder")


@blueprint.route("/<int:reminder_id>/edit", methods=["GET", "POST"])
def edit_reminder(reminder_id):  # Renamed for clarity
    """Edit a reminder."""
    reminder = Reminder.query.get_or_404(reminder_id)
    form = ReminderForm(request.form, obj=reminder)

    if form.validate_on_submit():
        form.populate_obj(reminder)
        db.session.commit()
        flash(
            f"Reminder for task '{reminder.task.name}' updated successfully.", "success")
        return redirect(url_for("reminders.list_reminders"))
    else:
        flash_errors(form)

    return render_template("reminders/form.html", form=form, title="Edit Reminder", reminder=reminder)


@blueprint.route("/<int:reminder_id>/delete", methods=["POST"])
def delete_reminder(reminder_id):  # Renamed for clarity
    """Delete a reminder."""
    reminder = Reminder.query.get_or_404(reminder_id)
    task_name = reminder.task.name  # Get task name before deleting
    db.session.delete(reminder)
    db.session.commit()
    flash(f"Reminder for task '{task_name}' deleted successfully.", "info")
    return redirect(url_for("reminders.list_reminders"))

# Removed complete() function as it's not part of the new model/requirements
