# -*- coding: utf-8 -*-
"""Task UI views."""
from flask import Blueprint, render_template, request, flash, redirect, url_for, abort

from ..forms import TaskForm
from ..models import Task, Project
from ..extensions import db
from ..utils import flash_errors

blueprint = Blueprint("tasks", __name__, url_prefix="/tasks")


@blueprint.route("/")
def list_tasks():
    """List all tasks with pagination and filtering."""
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 10, type=int)
    name_filter = request.args.get("name", None, type=str)
    project_id_filter = request.args.get("project_id", None, type=int)

    query = Task.query.join(Project)
    if name_filter:
        query = query.filter(Task.name.ilike(f"%{name_filter}%"))
    if project_id_filter:
        query = query.filter(Task.project_id == project_id_filter)  # type: ignore

    paginated_tasks = query.order_by(Project.name, Task.name).paginate(
        page=page, per_page=per_page, error_out=False)

    projects = Project.query.order_by(
        Project.name).all()  # For filter dropdown

    return render_template("tasks/list.html", paginated_tasks=paginated_tasks, name_filter=name_filter, project_id_filter=project_id_filter, projects=projects)


@blueprint.route("/<int:task_id>")
def view_task(task_id):
    """View a specific task with its details."""
    task = Task.query.get_or_404(task_id)
    return render_template("tasks/detail.html", task=task)


@blueprint.route("/new", methods=["GET", "POST"])
def new_task():
    """Create a new task."""
    form = TaskForm(request.form)
    if form.validate_on_submit():
        # Create the task with core attributes
        # (status, priority, and due_date are not stored in the database yet)
        task = Task.create(
            name=form.name.data,
            description=form.description.data,
            project_id=form.project_id.data,
            parent_task_id=form.parent_task_id.data or None  # Handle empty selection
        )
        flash(f"Task '{task.name}' created successfully.", "success")
        return redirect(url_for("tasks.list_tasks"))
    else:
        flash_errors(form)
    return render_template("tasks/form.html", form=form, title="New Task")


@blueprint.route("/<int:task_id>/edit", methods=["GET", "POST"])
def edit_task(task_id):
    """Edit a task."""
    task = Task.query.get_or_404(task_id)
    form = TaskForm(request.form, obj=task)

    if form.validate_on_submit():
        # Update core task attributes
        # (status, priority, and due_date are not stored in the database yet)
        task.name = form.name.data
        task.description = form.description.data
        task.project_id = form.project_id.data
        task.parent_task_id = form.parent_task_id.data or None  # Handle empty selection
        db.session.commit()
        flash(f"Task '{task.name}' updated successfully.", "success")
        return redirect(url_for("tasks.list_tasks"))
    else:
        flash_errors(form)

    return render_template("tasks/form.html", form=form, title="Edit Task", task=task)


@blueprint.route("/<int:task_id>/delete", methods=["POST"])
def delete_task(task_id):
    """Delete a task."""
    task = Task.query.get_or_404(task_id)
    name = task.name
    db.session.delete(task)
    db.session.commit()
    flash(f"Task '{name}' deleted successfully.", "info")
    return redirect(url_for("tasks.list_tasks"))
